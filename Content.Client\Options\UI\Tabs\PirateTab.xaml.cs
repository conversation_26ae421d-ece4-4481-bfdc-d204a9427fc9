using System;
using Content.Shared._Pirate.CCVar;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Configuration;
using Robust.Shared.IoC;
using Robust.Shared.Maths;

namespace Content.Client.Options.UI.Tabs;

[GenerateTypedNameReferences]
public sealed partial class PirateTab : Control
{
    [Dependency] private readonly IConfigurationManager _cfg = default!;

    public PirateTab()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);

        PingNameEnabledCheckBox.OnToggled += OnPingNameEnabledToggled;
        PingNameSoundsCheckBox.OnToggled += OnPingNameSoundsToggled;
        PingNameColorLineEdit.OnTextChanged += OnPingNameColorChanged;
        PingNameColorReset.OnPressed += OnPingNameColorReset;

        UpdateValues();
        UpdateTestLabel();
    }

    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            PingNameEnabledCheckBox.OnToggled -= OnPingNameEnabledToggled;
            PingNameSoundsCheckBox.OnToggled -= OnPingNameSoundsToggled;
            PingNameColorLineEdit.OnTextChanged -= OnPingNameColorChanged;
            PingNameColorReset.OnPressed -= OnPingNameColorReset;
        }

        base.Dispose(disposing);
    }

    private void UpdateValues()
    {
        PingNameEnabledCheckBox.Pressed = _cfg.GetCVar(PirateCVars.PingNameEnabled);
        PingNameSoundsCheckBox.Pressed = _cfg.GetCVar(PirateCVars.PingNameSoundsEnabled);
        PingNameColorLineEdit.Text = _cfg.GetCVar(PirateCVars.PingNameColor);

        UpdateColorPreview();
    }

    private void UpdateColorPreview()
    {
        var colorHex = PingNameColorLineEdit.Text;
        var color = Color.TryFromHex(colorHex);
        if (color.HasValue)
        {
            PingNameColorPreview.Modulate = color.Value;
        }
        else
        {
            PingNameColorPreview.Modulate = Color.Gray;
        }
    }

    private void UpdateTestLabel()
    {
        var colorHex = _cfg.GetCVar(PirateCVars.PingNameColor);
        var enabled = _cfg.GetCVar(PirateCVars.PingNameEnabled);

        var color = Color.TryFromHex(colorHex);
        if (enabled && color.HasValue)
        {
            var colorHexFormatted = color.Value.ToHex();
            TestLabel.SetMessage($"Приклад: Привіт, [color={colorHexFormatted}]Ярем[/color]! Як справи?");
        }
        else
        {
            TestLabel.SetMessage("Приклад: Привіт, Ярем! Як справи?");
        }
    }

    private void OnPingNameEnabledToggled(BaseButton.ButtonToggledEventArgs args)
    {
        _cfg.SetCVar(PirateCVars.PingNameEnabled, args.Pressed);
        UpdateTestLabel();
    }

    private void OnPingNameSoundsToggled(BaseButton.ButtonToggledEventArgs args)
    {
        _cfg.SetCVar(PirateCVars.PingNameSoundsEnabled, args.Pressed);
    }

    private void OnPingNameColorChanged(LineEdit.LineEditEventArgs args)
    {
        var colorText = args.Text;

        // Ensure it starts with #
        if (!colorText.StartsWith("#") && colorText.Length > 0)
        {
            colorText = "#" + colorText;
            PingNameColorLineEdit.Text = colorText;
            return;
        }

        // Validate hex color
        var color = Color.TryFromHex(colorText);
        if (color.HasValue)
        {
            _cfg.SetCVar(PirateCVars.PingNameColor, colorText);
            UpdateTestLabel();
        }

        UpdateColorPreview();
    }

    private void OnPingNameColorReset(BaseButton.ButtonEventArgs args)
    {
        var defaultColor = "#FFFF00"; // Yellow
        PingNameColorLineEdit.Text = defaultColor;
        _cfg.SetCVar(PirateCVars.PingNameColor, defaultColor);
        UpdateColorPreview();
        UpdateTestLabel();
    }
}

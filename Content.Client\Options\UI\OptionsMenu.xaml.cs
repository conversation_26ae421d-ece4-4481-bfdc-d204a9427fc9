using Content.Client.Options.UI.Tabs;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.CustomControls;
using Robust.Client.UserInterface.XAML;


namespace Content.Client.Options.UI
{
    [GenerateTypedNameReferences]
    public sealed partial class OptionsMenu : DefaultWindow
    {
        public OptionsMenu()
        {
            RobustXamlLoader.Load(this);
            IoCManager.InjectDependencies(this);

            Tabs.SetTabTitle(0, Loc.GetString("ui-options-tab-misc"));
            Tabs.SetTabTitle(1, Loc.GetString("ui-options-tab-graphics"));
            Tabs.SetTabTitle(2, Loc.GetString("ui-options-tab-controls"));
            Tabs.SetTabTitle(3, Loc.GetString("ui-options-tab-audio"));
            Tabs.SetTabTitle(4, Loc.GetString("ui-options-tab-network"));
            Tabs.SetTabTitle(5, Loc.GetString("ui-options-tab-pirate"));

            UpdateTabs();
        }

        public void UpdateTabs()
        {
            GraphicsTab.UpdateProperties();
        }
    }
}

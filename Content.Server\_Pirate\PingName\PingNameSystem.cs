using Content.Server.Mind;
using Content.Shared._Pirate.PingName;
using Content.Shared.GameTicking;
using Content.Shared.Mind;
using Content.Shared.Mind.Components;
using Content.Shared.Players;
using Robust.Server.Player;
using Robust.Shared.Player;

namespace Content.Server._Pirate.PingName;

public sealed class PingNameSystem : SharedPingNameSystem
{
    [Dependency] private readonly MindSystem _mindSystem = default!;

    public override void Initialize()
    {
        base.Initialize();

        SubscribeLocalEvent<PlayerSpawnCompleteEvent>(OnPlayerSpawnComplete);
        SubscribeLocalEvent<MindAddedMessage>(OnMindAdded);
        SubscribeLocalEvent<RoundRestartCleanupEvent>(OnRoundRestart);
    }

    /// <summary>
    /// When a player spawns, set up their ping name component.
    /// </summary>
    private void OnPlayerSpawnComplete(PlayerSpawnCompleteEvent ev)
    {
        SetupPingNameForPlayer(ev.Player, ev.Mob);
    }

    /// <summary>
    /// When a mind is added to an entity, set up ping name component.
    /// </summary>
    private void OnMindAdded(MindAddedMessage ev)
    {
        if (_mindSystem.TryGetSession(ev.Mind, out var session))
        {
            SetupPingNameForPlayer(session, ev.Mind);
        }
    }

    /// <summary>
    /// Clean up ping name components on round restart.
    /// </summary>
    private void OnRoundRestart(RoundRestartCleanupEvent ev)
    {
        var query = EntityQueryEnumerator<PingNameComponent>();
        while (query.MoveNext(out var uid, out _))
        {
            RemComp<PingNameComponent>(uid);
        }
    }

    /// <summary>
    /// Sets up the ping name component for a player.
    /// </summary>
    private void SetupPingNameForPlayer(ICommonSession session, EntityUid mindEntity)
    {
        if (!_mindSystem.TryGetMind(session.UserId, out var mindId) || mindId.Value.Owner != mindEntity)
            return;

        // Add the component to the mind entity
        var comp = EnsureComp<PingNameComponent>(mindEntity);

        // Get the player's character name
        if (session.AttachedEntity is { Valid: true } playerEntity &&
            TryComp<MetaDataComponent>(playerEntity, out var meta))
        {
            UpdateNameParts(mindEntity, meta.EntityName, comp);
        }
    }

    /// <summary>
    /// Updates the ping name component when a player's name changes.
    /// </summary>
    public void UpdatePlayerName(ICommonSession session, string newName)
    {
        if (!_mindSystem.TryGetMind(session.UserId, out var mindId))
            return;

        if (TryComp<PingNameComponent>(mindId.Value.Owner, out var comp))
        {
            UpdateNameParts(mindId.Value.Owner, newName, comp);
        }
    }

    /// <summary>
    /// Enables or disables name highlighting for a player.
    /// </summary>
    public void SetPingNameEnabled(ICommonSession session, bool enabled)
    {
        if (!_mindSystem.TryGetMind(session.UserId, out var mindId))
            return;

        var comp = EnsureComp<PingNameComponent>(mindId.Value.Owner);
        comp.Enabled = enabled;
        Dirty(mindId.Value.Owner, comp);
    }

    /// <summary>
    /// Gets whether name highlighting is enabled for a player.
    /// </summary>
    public bool IsPingNameEnabled(ICommonSession session)
    {
        if (!_mindSystem.TryGetMind(session.UserId, out var mindId))
            return false;

        return TryComp<PingNameComponent>(mindId.Value.Owner, out var comp) && comp.Enabled;
    }
}

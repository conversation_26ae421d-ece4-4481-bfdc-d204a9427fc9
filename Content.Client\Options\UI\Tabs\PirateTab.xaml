<tabs:PirateTab xmlns="https://spacestation14.io"
                 xmlns:tabs="clr-namespace:Content.Client.Options.UI.Tabs"
                 xmlns:cc="clr-namespace:Content.Client.Administration.UI.CustomControls"
                 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                 x:Class="Content.Client.Options.UI.Tabs.PirateTab">
    <ScrollContainer>
        <BoxContainer Orientation="Vertical" Margin="2">
            <!-- Name Highlighting Section -->
            <Label Text="{Loc 'ui-options-pirate-name-highlighting'}"
                   StyleClasses="LabelHeading"
                   Margin="0 0 0 8"/>

            <cc:HSeparator/>

            <BoxContainer Orientation="Horizontal" Margin="2">
                <CheckBox Name="PingNameEnabledCheckBox"
                         Text="{Loc 'ui-options-pirate-ping-name-enabled'}"
                         ToolTip="{Loc 'ui-options-pirate-ping-name-enabled-tooltip'}"/>
            </BoxContainer>

            <BoxContainer Orientation="Horizontal" Margin="2">
                <CheckBox Name="PingNameSoundsCheckBox"
                         Text="{Loc 'ui-options-pirate-ping-name-sounds'}"
                         ToolTip="{Loc 'ui-options-pirate-ping-name-sounds-tooltip'}"/>
            </BoxContainer>

            <BoxContainer Orientation="Horizontal" Margin="2">
                <Label Text="{Loc 'ui-options-pirate-ping-name-color'}"
                       MinSize="150 0"
                       VerticalAlignment="Center"/>
                <LineEdit Name="PingNameColorLineEdit"
                         MinSize="100 0"
                         PlaceHolder="#FFFF00"
                         ToolTip="{Loc 'ui-options-pirate-ping-name-color-tooltip'}"/>
                <Button Name="PingNameColorPreview"
                       Text="■"
                       MinSize="30 30"
                       Margin="4 0 0 0"
                       ToolTip="{Loc 'ui-options-pirate-ping-name-color-preview-tooltip'}"/>
                <Button Name="PingNameColorReset"
                       Text="{Loc 'ui-options-pirate-ping-name-color-reset'}"
                       Margin="4 0 0 0"
                       ToolTip="{Loc 'ui-options-pirate-ping-name-color-reset-tooltip'}"/>
            </BoxContainer>

            <!-- Test Section -->
            <Label Text="{Loc 'ui-options-pirate-test'}"
                   StyleClasses="LabelHeading"
                   Margin="0 8 0 8"/>

            <cc:HSeparator/>

            <BoxContainer Orientation="Vertical" Margin="2">
                <Label Text="{Loc 'ui-options-pirate-test-description'}"
                       Margin="0 0 0 4"/>
                <RichTextLabel Name="TestLabel"
                              Margin="4"
                              MinSize="0 40"
                              StyleClasses="LabelSubText"/>
            </BoxContainer>
        </BoxContainer>
    </ScrollContainer>
</tabs:PirateTab>

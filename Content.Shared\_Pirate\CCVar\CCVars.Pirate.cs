using Robust.Shared;
using Robust.Shared.Configuration;

namespace Content.Shared._Pirate.CCVar;

/// <summary>
/// Pirate-specific CVars
/// </summary>
[CVarDefs]
public sealed class PirateCVars
{
    /// <summary>
    /// Whether name highlighting is enabled
    /// </summary>
    public static readonly CVarDef<bool> PingNameEnabled =
        CVarDef.Create("pirate.ping_name_enabled", true, CVar.CLIENTONLY | CVar.ARCHIVE);

    /// <summary>
    /// Whether ping name sounds are enabled (for future use)
    /// </summary>
    public static readonly CVarDef<bool> PingNameSoundsEnabled =
        CVarDef.Create("pirate.ping_name_sounds_enabled", true, CVar.CLIENTONLY | CVar.ARCHIVE);

    /// <summary>
    /// Color for highlighted names in hex format (e.g., "#FFFF00" for yellow)
    /// </summary>
    public static readonly CVarDef<string> PingNameColor =
        CVarDef.Create("pirate.ping_name_color", "#FFFF00", CVar.CLIENTONLY | CVar.ARCHIVE);
}
